<script setup lang="ts">
import { FlexRender, useVueTable } from '@tanstack/vue-table';

const table = useVueTable({
    data: [
        {
            "firstName": "<PERSON>",
            "lastName": "<PERSON><PERSON>",
            "age": 33,
            "visits": 100,
            "progress": 50,
            "status": "Married"
        },
        {
            "firstName": "<PERSON>",
            "lastName": "Van<PERSON>",
            "age": 27,
            "visits": 200,
            "progress": 100,
            "status": "Single"
        }
    ] as {
        firstName: string
        lastName: string
        age: number
        visits: number
        progress: number
        status: string
    }[],
});
</script>

<template>
    <tbody>
        <tr v-for="row in table.getRowModel().rows" :key="row.id">
            <td v-for="cell in row.getVisibleCells()" :key="cell.id">
                <FlexRender :render="cell.column.columnDef.cell" :props="cell.getContext()" />
            </td>
        </tr>
    </tbody>
</template>