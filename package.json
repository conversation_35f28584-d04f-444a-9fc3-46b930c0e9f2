{"name": "nuxt-app", "type": "module", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@headlessui/tailwindcss": "^0.2.2", "@heroicons/vue": "^2.2.0", "@nuxt/content": "^3.6.3", "@nuxt/eslint": "^1.8.0", "@nuxt/fonts": "^0.11.4", "@nuxt/icon": "^2.0.0", "@nuxt/image": "^1.11.0", "@nuxt/scripts": "^0.11.10", "@nuxt/test-utils": "^3.19.2", "@pinia/nuxt": "^0.11.2", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.12", "@tanstack/vue-query": "^5.85.3", "@tanstack/vue-table": "^8.21.3", "@unhead/vue": "^2.0.14", "eslint": "^9.33.0", "nuxt": "^4.0.3", "nuxt-headlessui": "^1.2.1", "pinia": "^3.0.3", "tailwindcss": "^4.1.12", "tailwindcss-animate": "^1.0.7", "vue": "^3.5.18", "vue-router": "^4.5.1"}}